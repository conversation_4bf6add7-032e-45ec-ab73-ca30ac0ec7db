version: '3.8'

services:
  agent-orchestrator:
    build: .
    ports:
      - "8095:8095"
    environment:
      - PORT=8095
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8095/api/v1/monitoring/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.agent-orchestrator.rule=Host(`orchestrator.localhost`)"
      - "traefik.http.services.agent-orchestrator.loadbalancer.server.port=8095"

  # Optional: Redis for future extensions
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
